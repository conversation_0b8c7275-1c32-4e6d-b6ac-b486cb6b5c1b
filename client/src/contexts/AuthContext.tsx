import React, { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import { useWeb3 } from '@/contexts/Web3Context'; // Import useWeb3
import { useLocation } from 'wouter';

// Define the auth method type based on observed linter expectations.
export type AuthMethodType = 'orangeID' | 'wallet' | null;

// Session timeout in milliseconds (12 hours)
const SESSION_TIMEOUT_MS = 12 * 60 * 60 * 1000;

// Define the context type
interface AuthContextType {
  isLoggedIn: boolean;
  currentUser: any;
  authMethod: AuthMethodType; // This will now use the more specific type
  orangeIDUser: any;
  isOrangeIDLoggedIn: boolean;
  isWalletConnected: boolean;
  walletAddress: string | null;
  logout: () => Promise<void>;
  linkWalletToOrangeID: () => Promise<boolean>;
  isAuthLoading: boolean;
  walletUser: { walletAddress: string; displayName: string; } | null;
  ensureValidSession: () => boolean;
  checkSessionExpiry: () => void; // Export session check function
}

// Default value for the context
const defaultAuthContext: AuthContextType = {
  isLoggedIn: false,
  currentUser: null,
  authMethod: null, // Initial state is null, consistent with AuthMethodType
  orangeIDUser: null,
  isOrangeIDLoggedIn: false,
  isWalletConnected: false,
  walletAddress: null,
  logout: async () => {},
  linkWalletToOrangeID: async () => false,
  isAuthLoading: true,
  walletUser: null,
  ensureValidSession: () => true,
  checkSessionExpiry: () => {}, // Default empty function
};

// Create the context with default values
const AuthContext = createContext<AuthContextType>(defaultAuthContext);

// Auth Provider Props
interface AuthProviderProps {
  children: ReactNode;
}

// Helper function to check if session is expired
const isSessionExpired = (): boolean => {
  const lastLoginTimestamp = localStorage.getItem('lastLoginTimestamp');

  if (!lastLoginTimestamp) return false;

  const loginTime = parseInt(lastLoginTimestamp, 10);
  const currentTime = Date.now();

  return (currentTime - loginTime) > SESSION_TIMEOUT_MS;
};

// Helper function to update the login timestamp
const updateLoginTimestamp = (): void => {
  localStorage.setItem('lastLoginTimestamp', Date.now().toString());
};

// Auth Provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // State for auth
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [authMethod, setAuthMethod] = useState<AuthMethodType>(null); // State uses the specific AuthMethodType
  const [isAuthLoading, setIsAuthLoading] = useState<boolean>(true);
  const [location, setLocation] = useLocation();

  // Orange ID specific state
  const [orangeIDUser, setOrangeIDUser] = useState<any>(null);
  const [isOrangeIDLoggedIn, setIsOrangeIDLoggedIn] = useState<boolean>(false);

  // Wallet specific state
  const [isWalletConnectedState, setIsWalletConnectedState] = useState<boolean>(false); // Renamed to avoid conflict
  const [walletAddressState, setWalletAddressState] = useState<string | null>(null); // Renamed to avoid conflict
  const [walletUser, setWalletUser] = useState<{ walletAddress: string; displayName: string; } | null>(null); // State for wallet user info

  // Initialize auth state from localStorage on component mount
  // Store wallet auth state in a ref to persist between renders
  const walletAuthRef = React.useRef<{
    isWalletLoggedIn: boolean;
    walletUserData: { walletAddress: string; displayName: string; } | null;
  }>({
    isWalletLoggedIn: false,
    walletUserData: null
  });

  // Effect to handle Web3 wallet connection status
  const { isConnected: web3IsConnected, account: web3Account, disconnectWallet: web3DisconnectWallet, isConnecting: web3IsConnecting } = useWeb3();

  // Logout function
  const logout = async (): Promise<void> => {
    const currentAuthMethod = authMethod; // Capture authMethod before it's reset

    // Clear Orange ID data if logged in with Orange ID
    if (isOrangeIDLoggedIn) {
      localStorage.removeItem('orangeIDUser');
      localStorage.removeItem('orangeIDToken');
      localStorage.removeItem('orangeIDRefreshToken');
      setOrangeIDUser(null);
      setIsOrangeIDLoggedIn(false);
    }

    // Clear session timestamp to prevent login loops
    localStorage.removeItem('lastLoginTimestamp');

    // Reset overall auth state
    setIsLoggedIn(false);
    setCurrentUser(null);
    setAuthMethod(null); // This is a valid AuthMethodType
    setIsAuthLoading(false);

    // If logged out from wallet, also disconnect wallet
    if (currentAuthMethod === 'wallet') {
      await web3DisconnectWallet();
    }

    setWalletUser(null); // Clear wallet user info

    window.location.href = '/';
  };

  // Check if session is expired and log out if needed
  const checkSessionExpiry = React.useCallback(() => {
    if (isSessionExpired() && isLoggedIn) {
      logout();
    }
  }, [isLoggedIn, logout]);

  // Proactive session check - call this before any authenticated actions
  const ensureValidSession = React.useCallback(() => {
    if (isLoggedIn && isSessionExpired()) {
      logout();
      return false;
    }
    return true;
  }, [isLoggedIn, logout]);

  // Check session when navigating to home
  useEffect(() => {
    if (location === '/' || location === '/home') {
      checkSessionExpiry();
    }
  }, [location, checkSessionExpiry]);

  useEffect(() => {
    // Function to load user from localStorage
    const loadUserFromStorage = () => {
      setIsAuthLoading(true); // Explicitly set loading true at the start of this operation

      // Check for session expiry first
      if (isSessionExpired()) {
        localStorage.removeItem('orangeIDUser');
        localStorage.removeItem('orangeIDToken');
        localStorage.removeItem('orangeIDRefreshToken');
        localStorage.removeItem('lastLoginTimestamp');
        setIsLoggedIn(false);
        setCurrentUser(null);
        setAuthMethod(null);
        setOrangeIDUser(null);
        setIsOrangeIDLoggedIn(false);
        setIsAuthLoading(false);
        return;
      }

      // Check if we detected wallet login from web3 provider
      if (walletAuthRef.current.isWalletLoggedIn && walletAuthRef.current.walletUserData) {
        setIsLoggedIn(true);
        setAuthMethod('wallet');
        setWalletUser(walletAuthRef.current.walletUserData);
        setCurrentUser(walletAuthRef.current.walletUserData);
        setIsOrangeIDLoggedIn(false);
        setOrangeIDUser(null);
        setIsAuthLoading(false);
        updateLoginTimestamp(); // Update the timestamp on successful login
        return;
      }

      const storedOrangeUser = localStorage.getItem('orangeIDUser');
      if (storedOrangeUser && storedOrangeUser !== 'undefined' && storedOrangeUser !== 'null') {
        try {
          const parsedUser = JSON.parse(storedOrangeUser);
          setOrangeIDUser(parsedUser);
          setCurrentUser(parsedUser);
          setIsOrangeIDLoggedIn(true);
          setIsLoggedIn(true);
          setAuthMethod('orangeID');
          updateLoginTimestamp(); // Update the timestamp on successful login
        } catch (e) {
          console.error('AuthContext: Error parsing stored Orange ID user:', e);
          localStorage.removeItem('orangeIDUser');
          // Ensure states are reset if parsing fails
          setOrangeIDUser(null);
          setCurrentUser(null);
          setIsOrangeIDLoggedIn(false);

          // Don't reset login state if we have a wallet connection
          if (!walletAuthRef.current.isWalletLoggedIn) {
            setIsLoggedIn(false);
            setAuthMethod(null);
          }
        }
      } else {
        setOrangeIDUser(null);
        setCurrentUser(null);
        setIsOrangeIDLoggedIn(false);

        // Only reset login state if we don't have a wallet connection
        if (!walletAuthRef.current.isWalletLoggedIn) {
          setIsLoggedIn(false);
          setAuthMethod(null);
        }
      }
      setIsAuthLoading(false);
    };

    // Initial load - check session on app start
    checkSessionExpiry();
    loadUserFromStorage();

    // Listen for our custom login success event
    const handleLoginSuccess = () => {
      // Only reload from storage if we don't have wallet auth
      if (!walletAuthRef.current.isWalletLoggedIn) {
        loadUserFromStorage();
      } else {
        // Make sure login state is properly set
        setIsLoggedIn(true);
        setAuthMethod('wallet');
        updateLoginTimestamp(); // Update the timestamp on login success
      }
    };

    window.addEventListener('auth-login-success', handleLoginSuccess);

    // Set up visibility change listener to check session expiry when tab becomes active
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        checkSessionExpiry();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Removed the interval-based session check

    return () => {
      window.removeEventListener('auth-login-success', handleLoginSuccess);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [checkSessionExpiry]);

  useEffect(() => {
    setIsWalletConnectedState(web3IsConnected);
    setWalletAddressState(web3Account);

    if (web3IsConnected && web3Account) {
      // Create wallet info object
      const displayName = `${web3Account.substring(0, 6)}...${web3Account.substring(web3Account.length - 4)}`;
      const walletInfo = { walletAddress: web3Account, displayName };

      // Store in ref for persistence across renders
      walletAuthRef.current = {
        isWalletLoggedIn: true,
        walletUserData: walletInfo
      };

      // Update state (only if not already set)
      if (!isLoggedIn || authMethod !== 'wallet') {
        setWalletUser(walletInfo);
        setCurrentUser(walletInfo);
        setIsLoggedIn(true);
        setAuthMethod('wallet');
        setIsOrangeIDLoggedIn(false);
        setOrangeIDUser(null);
        updateLoginTimestamp(); // Update timestamp on wallet connect

        // Dispatch a login success event for other parts of the app that might rely on it
        window.dispatchEvent(new CustomEvent('auth-login-success'));

        // Redirect to home page on successful wallet login
        const currentPath = window.location.pathname;
        if (currentPath === '/' || currentPath === '') {
          setTimeout(() => {
            setLocation('/home');
          }, 100); // Small delay to ensure state updates are processed
        }
      }
    } else if (!web3IsConnected && authMethod === 'wallet' && isLoggedIn) {
      // If wallet disconnected and it was the auth method, log out
      // Clear wallet auth ref
      walletAuthRef.current = {
        isWalletLoggedIn: false,
        walletUserData: null
      };

      logout();
    }
  }, [web3IsConnected, web3Account, authMethod, isLoggedIn, setLocation]);

  // Effect to manage overall loading state
  useEffect(() => {
    // isAuthLoading should be false if OrangeID loading is done AND wallet is not in the process of connecting.
    // If OrangeID is done (loadUserFromStorage sets it to false),
    // and web3 is not currently trying to connect, then we are no longer loading.
    // If web3 IS connecting, we should wait for it.
    if (isAuthLoading && !web3IsConnecting && (orangeIDUser || localStorage.getItem('orangeIDUser') === null)) {
        // This condition means OrangeID check is complete (either user found or no user in storage)
        // AND web3 is not in a connecting state.
        // If web3 IS connected, the other useEffect will handle setting isLoggedIn.
        // If web3 is NOT connected and NOT connecting, then we can set loading to false.
        setIsAuthLoading(false);
    } else if (web3IsConnecting) {
        setIsAuthLoading(true); // If web3 is actively connecting, we are in a loading state.
    }
  }, [isAuthLoading, web3IsConnecting, orangeIDUser, web3IsConnected]);

  // Function to link wallet to Orange ID (placeholder)
  const linkWalletToOrangeID = async (): Promise<boolean> => {
    return false;
  };

  // Create the context value object
  const authContextValue: AuthContextType = {
    isLoggedIn,
    currentUser,
    authMethod,
    orangeIDUser,
    isOrangeIDLoggedIn,
    isWalletConnected: isWalletConnectedState,
    walletAddress: walletAddressState,
    logout,
    linkWalletToOrangeID,
    isAuthLoading,
    walletUser,
    ensureValidSession,
    checkSessionExpiry, // Export the session check function
  };

  // Expose auth context globally for components that can't use React context
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      // @ts-ignore - Global interface isn't correctly reflecting the updated type
      window.authContext = authContextValue;
    }

    return () => {
      if (typeof window !== 'undefined') {
        // @ts-ignore - Global interface isn't correctly reflecting the updated type
        delete window.authContext;
      }
    };
  }, [authContextValue]);

  return (
    <AuthContext.Provider value={authContextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);
