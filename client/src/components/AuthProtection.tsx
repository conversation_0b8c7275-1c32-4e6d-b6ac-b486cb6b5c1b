import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useAuth } from '@/contexts/AuthContext';

// Simple component to ensure pages are protected from unauthenticated access
const AuthProtection = () => {
  const { isLoggedIn, isAuthLoading, checkSessionExpiry } = useAuth();
  const [, setLocation] = useLocation();
  const [checkedStorage, setCheckedStorage] = useState(false);

  useEffect(() => {
    // Check for session expiry first
    checkSessionExpiry();
    
    // First check localStorage directly
    const storedUser = localStorage.getItem('orangeIDUser');
    const walletAddress = localStorage.getItem('walletAddress');
    const hasStoredAuth = (storedUser && storedUser !== 'undefined' && storedUser !== 'null') || walletAddress;
    
    setCheckedStorage(true);
    
    // If auth is still loading, wait for it
    if (isAuthLoading) {
      console.log('AuthProtection: Auth state loading, waiting...');
      return;
    }

    // If no auth in context and no stored auth, redirect to login
    if (!isLoggedIn && !hasStoredAuth && !window.location.pathname.includes('/auth/callback')) {
      console.log('AuthProtection: User not authenticated, redirecting to login page');
      setLocation('/');
    }
  }, [isLoggedIn, isAuthLoading, setLocation, checkSessionExpiry]);

  // This component doesn't render anything
  return null;
};

export default AuthProtection; 